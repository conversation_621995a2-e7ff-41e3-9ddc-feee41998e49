{"name": "my-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.22", "lucide-react": "^0.544.0", "next": "15.5.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.5.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.13", "typescript": "^5"}}