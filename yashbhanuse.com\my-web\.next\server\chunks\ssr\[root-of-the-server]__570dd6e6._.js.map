{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/my-portfolio/yashbhanuse.com/my-web/src/app/components/container.tsx"], "sourcesContent": ["\r\nconst container = ({children, className} : {\r\n    className? : string ,\r\n    children : React.ReactNode,\r\n}) => {\r\n  return (\r\n    <div className= {\"max-w-5xl mx-auto dark:bg-dark-black bg-white shadow-md rounded-lg min-h-screen \" } >{children}</div>\r\n  )\r\n}\r\n\r\nexport default container\r\n"], "names": [], "mappings": ";;;;;;AACA,MAAM,YAAY,CAAC,EAAC,QAAQ,EAAE,SAAS,EAGtC;IACC,qBACE,8OAAC;QAAI,WAAY;kBAAuF;;;;;;AAE5G;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/my-portfolio/yashbhanuse.com/my-web/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport navbar from \"./components/ui/navbar\";\nimport Container from \"./components/container\";\nimport Navbar from \"./components/ui/navbar\";\n\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n     <Container className=\" p-4\">\n     \n      <h1 className=\"text-2xl font-bold\"> Hi I am <PERSON><PERSON></h1>\n      <p className=\"text-sm\" > I am a software developer who loves to build websites and apps efficiently stylishly and responsive</p>\n    \n     </Container>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACd,cAAA,8OAAC,iJAAS;YAAC,WAAU;;8BAEpB,8OAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,8OAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;;;;;;;AAK9B", "debugId": null}}]}